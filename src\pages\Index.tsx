
import React, { useState, useEffect } from "react";
import HeroCarousel from "@/components/HeroCarousel";
import CardGrid from "@/components/CardGrid";
import Header from "@/components/Header";
import Footer from "@/components/Footer";
// Adblocker components removed
import ImageDiagnostics from "@/components/ImageDiagnostics";
import { MediaItem } from "@/types/media";
import { Link } from "react-router-dom";
import PromoBannerContainer from "@/components/PromoBannerContainer";
import { scrollToTop } from "@/utils/scrollToTop";
import { getHomepageContent } from "@/utils/contentFilters";
import { useDynamicHomepage, getFallbackHomepageContent } from "@/utils/dynamicHomepage";
// Adblocker awareness tracking imports removed
import { Button } from "@/components/ui/button";
import apiService from "@/services/apiService";

const Index = () => {
  // State for content data (fallback)
  const [contentData, setContentData] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Use dynamic homepage content
  const dynamicHomepage = useDynamicHomepage();

  // Get fallback content for when dynamic loading fails
  const fallbackContent = getFallbackHomepageContent(contentData);

  // Use dynamic content if available, otherwise fallback
  const homepageData = dynamicHomepage.error ? fallbackContent : dynamicHomepage;

  // Adblocker awareness popup state removed

  // Test modal state removed

  // Fetch content data from API
  useEffect(() => {
    const fetchContent = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await apiService.getContent({
          published: true,
          limit: 100 // Get enough content for all sections
        });

        if (response && response.success && Array.isArray(response.data)) {
          setContentData(response.data);
        } else {
          console.warn('No content data received from API, using empty array');
          setContentData([]);
        }
      } catch (error) {
        console.error('Error fetching content:', error);
        setError('Failed to load content');
        setContentData([]); // Fallback to empty array
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, []);

  // Adblocker awareness popup useEffect and handler removed

  // Show loading state
  if (loading) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <div className="h-7 md:h-10" />
        <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center text-muted-foreground">
              <div className="text-4xl mb-4">🎬</div>
              <p className="text-lg">Loading content...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <div className="h-7 md:h-10" />
        <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center text-muted-foreground">
              <div className="text-4xl mb-4">⚠️</div>
              <p className="text-lg mb-2">Failed to load content</p>
              <p className="text-sm">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
              >
                Retry
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />

      {/* Added spacing here after Header */}
      <div className="h-7 md:h-10" />

      <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
        {/* Increased margin-bottom for HeroCarousel */}
        <div className="mb-6 sm:mb-9 md:mb-14">
          <HeroCarousel contentData={homepageData.carouselContent} />
        </div>

        {/* Proper gap between sections */}
        <div className="mb-8 sm:mb-12">
          <PromoBannerContainer />
        </div>

        {/* Dynamic Content Sections */}
        {homepageData.isLoading ? (
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-muted-foreground">Loading content sections...</span>
          </div>
        ) : homepageData.error ? (
          <div className="text-center py-12">
            <p className="text-destructive mb-4">Failed to load dynamic content: {homepageData.error}</p>
            <p className="text-muted-foreground">Showing fallback content...</p>
          </div>
        ) : null}

        {homepageData.sections.map((section, index) => (
          <section key={section.id} className="stdb-section mb-10 sm:mb-14">
            <div className="flex items-end justify-between mb-3 sm:mb-4">
              <h3
                className="
                  text-2xl sm:text-3xl md:text-4xl
                  font-black
                  font-mono
                  text-primary
                  drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)]
                  tracking-wide
                  uppercase
                  leading-tight
                  select-none
                  "
                style={{
                  letterSpacing: "1px",
                  color: section.color
                }}
              >
                {section.name}
              </h3>
              {section.showViewAll && (
                <Link
                  to={`/${section.slug}`}
                  className="text-primary font-bold underline underline-offset-4 text-sm sm:text-base hover:opacity-80 transition flex-shrink-0"
                  onClick={scrollToTop}
                  style={{ color: section.color }}
                >
                  Show all ({section.totalCount})
                </Link>
              )}
            </div>
            <CardGrid items={section.content} />
          </section>
        ))}

        {/* Show message if no sections available */}
        {!homepageData.isLoading && !homepageData.error && homepageData.sections.length === 0 && (
          <div className="text-center py-12">
            <p className="text-muted-foreground">No content sections configured. Please contact the administrator.</p>
          </div>
        )}
      </main>

      <Footer />

      {/* Development test button removed */}

      {/* Adblocker awareness popup and test modal removed */}
    </div>
  );
};

export default Index;
