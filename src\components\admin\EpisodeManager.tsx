import { useState, useEffect } from "react";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { MediaItem, Episode, Season } from "@/types/media";
import { EpisodeFormData, SeasonFormData } from "@/types/admin";
import { Plus, Edit, Trash2, Play, Calendar, Clock, Video } from "lucide-react";
import SecureVideoPlayer from "@/components/SecureVideoPlayer";
import { encodeVideoLinks, parseVideoLinks, isValidVideoLink } from "@/utils/videoSecurity";
import apiService from "@/services/apiService";

interface EpisodeManagerProps {
  isOpen: boolean;
  onClose: () => void;
  content: MediaItem | null;
  onSave: (updatedContent: MediaItem) => void;
}

export default function EpisodeManager({ isOpen, onClose, content, onSave }: EpisodeManagerProps) {
  const { toast } = useToast();
  const [selectedSeason, setSelectedSeason] = useState<number>(1);
  const [showAddEpisode, setShowAddEpisode] = useState(false);
  const [showAddSeason, setShowAddSeason] = useState(false);
  const [editingEpisode, setEditingEpisode] = useState<Episode | null>(null);
  const [editingSeason, setEditingSeason] = useState<Season | null>(null);
  const [seasons, setSeasons] = useState<Season[]>([]);
  const [loading, setLoading] = useState(false);

  const [episodeForm, setEpisodeForm] = useState<EpisodeFormData>({
    title: "",
    season: 1,
    episode: 1,
    description: "",
    videoLink: "",
    secureVideoLinks: "",
    runtime: "",
    airDate: "",
    thumbnailUrl: "",
  });

  const [seasonForm, setSeasonForm] = useState<SeasonFormData>({
    seasonNumber: 1,
    title: "",
    description: "",
    posterUrl: "",
  });

  // Load seasons from database when content changes
  useEffect(() => {
    if (content && content.type === "series" && isOpen) {
      loadSeasons();
    }
  }, [content, isOpen]);

  const loadSeasons = async () => {
    if (!content) return;

    setLoading(true);
    try {
      const result = await apiService.getContentSeasons(content.id);
      if (result.success) {
        setSeasons(result.data || []);
      } else {
        console.error('Failed to load seasons:', result.message);
        setSeasons([]);
      }
    } catch (error) {
      console.error('Error loading seasons:', error);
      setSeasons([]);
    } finally {
      setLoading(false);
    }
  };

  if (!content || content.type !== "series") return null;

  const currentSeason = seasons.find(s => s.seasonNumber === selectedSeason);
  const episodes = currentSeason?.episodes || [];

  const resetEpisodeForm = () => {
    const nextEpisodeNumber = episodes.length + 1;
    setEpisodeForm({
      title: "",
      season: selectedSeason,
      episode: nextEpisodeNumber,
      description: "",
      videoLink: "",
      secureVideoLinks: "",
      runtime: "",
      airDate: "",
      thumbnailUrl: "",
    });
  };

  const resetSeasonForm = () => {
    const nextSeasonNumber = Math.max(...seasons.map(s => s.seasonNumber), 0) + 1;
    setSeasonForm({
      seasonNumber: nextSeasonNumber,
      title: "",
      description: "",
      posterUrl: "",
    });
  };

  const handleAddEpisode = async () => {
    if (!episodeForm.title || !episodeForm.videoLink) {
      toast({
        title: "Error",
        description: "Please fill in episode title and video link",
        variant: "destructive",
      });
      return;
    }

    // Validate video links if provided
    if (episodeForm.videoLink) {
      const links = parseVideoLinks(episodeForm.videoLink);
      const validLinks = links.filter(link => isValidVideoLink(link));
      if (links.length > 0 && validLinks.length === 0) {
        toast({
          title: "Warning",
          description: "No valid video links found. Please check the format.",
          variant: "destructive",
        });
        return;
      }
    }

    if (!content) return;

    try {
      // First, ensure the season exists
      let targetSeason = seasons.find(s => s.seasonNumber === selectedSeason);

      if (!targetSeason) {
        // Create the season first
        const seasonResult = await apiService.createSeason(content.id, {
          seasonNumber: selectedSeason,
          title: `Season ${selectedSeason}`,
          description: "",
          posterUrl: ""
        });

        if (!seasonResult.success) {
          toast({
            title: "Error",
            description: seasonResult.message || "Failed to create season",
            variant: "destructive",
          });
          return;
        }

        // Reload seasons to get the new season with proper ID
        await loadSeasons();
        targetSeason = seasons.find(s => s.seasonNumber === selectedSeason);

        if (!targetSeason) {
          toast({
            title: "Error",
            description: "Failed to create season",
            variant: "destructive",
          });
          return;
        }
      }

      // Create the episode using the API
      const episodeData = {
        episodeNumber: episodeForm.episode,
        title: episodeForm.title,
        description: episodeForm.description,
        secureVideoLinks: episodeForm.secureVideoLinks,
        runtime: episodeForm.runtime,
        airDate: episodeForm.airDate,
        thumbnailUrl: episodeForm.thumbnailUrl
      };

      const result = await apiService.createEpisode(targetSeason.id, episodeData);

      if (result.success) {
        // Reload seasons to get updated data
        await loadSeasons();

        setShowAddEpisode(false);
        resetEpisodeForm();

        toast({
          title: "Episode added",
          description: `Episode ${episodeForm.episode} added to Season ${selectedSeason} successfully`,
        });

        // Update the parent content with new totals
        if (content) {
          const updatedContent: MediaItem = {
            ...content,
            totalSeasons: seasons.length,
            totalEpisodes: seasons.reduce((total, season) => total + (season.episodes?.length || 0), 0),
            updatedAt: new Date().toISOString(),
          };
          onSave(updatedContent);
        }
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to create episode",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error creating episode:', error);
      toast({
        title: "Error",
        description: "Failed to create episode",
        variant: "destructive",
      });
    }
  };

  const handleAddSeason = async () => {
    if (!seasonForm.seasonNumber) {
      toast({
        title: "Error",
        description: "Please specify season number",
        variant: "destructive",
      });
      return;
    }

    if (!content) return;

    try {
      const seasonData = {
        seasonNumber: seasonForm.seasonNumber,
        title: seasonForm.title || `Season ${seasonForm.seasonNumber}`,
        description: seasonForm.description,
        posterUrl: seasonForm.posterUrl
      };

      const result = await apiService.createSeason(content.id, seasonData);

      if (result.success) {
        // Reload seasons to get updated data
        await loadSeasons();

        setShowAddSeason(false);
        setSelectedSeason(seasonForm.seasonNumber);
        resetSeasonForm();

        toast({
          title: "Season added",
          description: `Season ${seasonForm.seasonNumber} added successfully`,
        });

        // Update the parent content with new totals
        const updatedContent: MediaItem = {
          ...content,
          totalSeasons: seasons.length + 1, // +1 because seasons state hasn't updated yet
          totalEpisodes: content.totalEpisodes || 0,
          updatedAt: new Date().toISOString(),
        };
        onSave(updatedContent);
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to create season",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Error creating season:', error);
      toast({
        title: "Error",
        description: "Failed to create season",
        variant: "destructive",
      });
    }
  };

  const handleDeleteEpisode = async (episodeId: string) => {
    const episode = episodes.find(e => e.id === episodeId);
    if (!episode) return;

    if (window.confirm(`Are you sure you want to delete "${episode.title}"?`)) {
      try {
        const result = await apiService.deleteEpisode(episodeId);

        if (result.success) {
          // Reload seasons to get updated data
          await loadSeasons();

          toast({
            title: "Episode deleted",
            description: `"${episode.title}" has been deleted`,
          });

          // Update the parent content with new totals
          if (content) {
            const updatedContent: MediaItem = {
              ...content,
              totalEpisodes: (content.totalEpisodes || 0) - 1,
              updatedAt: new Date().toISOString(),
            };
            onSave(updatedContent);
          }
        } else {
          toast({
            title: "Error",
            description: result.message || "Failed to delete episode",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Error deleting episode:', error);
        toast({
          title: "Error",
          description: "Failed to delete episode",
          variant: "destructive",
        });
      }
    }
  };

  const handleDeleteSeason = (seasonNumber: number) => {
    const season = seasons.find(s => s.seasonNumber === seasonNumber);
    if (!season) return;

    if (window.confirm(`Are you sure you want to delete Season ${seasonNumber} and all its episodes?`)) {
      const updatedSeasons = seasons.filter(s => s.seasonNumber !== seasonNumber);

      const updatedContent: MediaItem = {
        ...content,
        seasons: updatedSeasons,
        totalSeasons: updatedSeasons.length,
        totalEpisodes: updatedSeasons.reduce((total, season) => total + season.episodes.length, 0),
        updatedAt: new Date().toISOString(),
      };

      onSave(updatedContent);
      
      // Switch to first available season or season 1
      if (updatedSeasons.length > 0) {
        setSelectedSeason(updatedSeasons[0].seasonNumber);
      } else {
        setSelectedSeason(1);
      }
      
      toast({
        title: "Season deleted",
        description: `Season ${seasonNumber} and all its episodes have been deleted`,
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Episodes: {content.title}</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Season Selection and Management */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Label>Season:</Label>
              <div className="flex gap-2">
                {seasons.map(season => (
                  <Button
                    key={season.seasonNumber}
                    variant={selectedSeason === season.seasonNumber ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedSeason(season.seasonNumber)}
                  >
                    Season {season.seasonNumber}
                    <Badge variant="secondary" className="ml-2">
                      {season.episodes.length}
                    </Badge>
                  </Button>
                ))}
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  resetSeasonForm();
                  setShowAddSeason(true);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Season
              </Button>
              {currentSeason && (
                <Button
                  variant="outline"
                  size="sm"
                  className="text-destructive hover:text-destructive"
                  onClick={() => handleDeleteSeason(selectedSeason)}
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Season
                </Button>
              )}
            </div>
          </div>

          {/* Episodes List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">
                Season {selectedSeason} Episodes ({episodes.length})
              </h3>
              <Button
                onClick={() => {
                  resetEpisodeForm();
                  setShowAddEpisode(true);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Episode
              </Button>
            </div>

            {episodes.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No episodes in this season. Add your first episode!
              </div>
            ) : (
              <div className="grid gap-4">
                {episodes
                  .sort((a, b) => a.episode - b.episode)
                  .map(episode => (
                    <div
                      key={episode.id}
                      className="border border-border rounded-lg p-4 space-y-2"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">
                              S{episode.season}E{episode.episode}
                            </Badge>
                            <h4 className="font-semibold">{episode.title}</h4>
                          </div>
                          {episode.description && (
                            <p className="text-sm text-muted-foreground mt-1">
                              {episode.description}
                            </p>
                          )}
                          <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                            {episode.runtime && (
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {episode.runtime} min
                              </div>
                            )}
                            {episode.airDate && (
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {new Date(episode.airDate).toLocaleDateString()}
                              </div>
                            )}
                            {episode.videoLink && (
                              <div className="flex items-center gap-1">
                                <Play className="h-3 w-3" />
                                Video Available
                              </div>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setEditingEpisode(episode)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => handleDeleteEpisode(episode.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>

          {/* Close Button */}
          <div className="flex justify-end pt-4 border-t">
            <Button onClick={onClose}>
              Close
            </Button>
          </div>
        </div>
      </DialogContent>

      {/* Add Episode Dialog */}
      <Dialog open={showAddEpisode} onOpenChange={setShowAddEpisode}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Add Episode to Season {selectedSeason}</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Episode Title *</Label>
                <Input
                  value={episodeForm.title}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Episode title"
                />
              </div>
              <div>
                <Label>Season</Label>
                <Input
                  type="number"
                  value={episodeForm.season}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, season: parseInt(e.target.value) || 1 }))}
                  min="1"
                />
              </div>
              <div>
                <Label>Episode Number</Label>
                <Input
                  type="number"
                  value={episodeForm.episode}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, episode: parseInt(e.target.value) || 1 }))}
                  min="1"
                />
              </div>
            </div>

            <div>
              <Label>Description</Label>
              <Textarea
                value={episodeForm.description}
                onChange={(e) => setEpisodeForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Episode description"
                rows={3}
              />
            </div>

            <div>
              <Label>Add Video Embed Link(s) *</Label>
              <Textarea
                placeholder="Paste embed iframe, e.g. //player.com/embed/... (one per line for multiple sources)"
                className="mt-1 min-h-[80px]"
                value={episodeForm.videoLink}
                onChange={(e) => {
                  const value = e.target.value;
                  setEpisodeForm(prev => ({ ...prev, videoLink: value }));
                  // Auto-encode for security when links are added
                  if (value.trim()) {
                    const encoded = encodeVideoLinks(value);
                    setEpisodeForm(prev => ({ ...prev, secureVideoLinks: encoded }));
                  } else {
                    setEpisodeForm(prev => ({ ...prev, secureVideoLinks: "" }));
                  }
                }}
              />
              <div className="text-xs text-muted-foreground mt-1">
                Supports multiple embed links (one per line). Links are automatically validated and secured.
              </div>
            </div>

            <div>
              <Label>Preview Player</Label>
              {episodeForm.secureVideoLinks ? (
                <SecureVideoPlayer
                  encodedVideoLinks={episodeForm.secureVideoLinks}
                  title={episodeForm.title || "Episode Preview"}
                  className="mt-2 max-w-2xl"
                  showPlayerSelection={true}
                />
              ) : (
                <div className="rounded-lg bg-background border border-border shadow-inner mt-2 max-w-2xl">
                  <div className="w-full aspect-video flex items-center justify-center text-muted-foreground">
                    <div className="text-center">
                      <Video className="w-12 h-12 mx-auto mb-2 opacity-50" />
                      <p>Add video links above to preview player</p>
                    </div>
                  </div>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label>Runtime (minutes)</Label>
                <Input
                  value={episodeForm.runtime}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, runtime: e.target.value }))}
                  placeholder="45"
                />
              </div>
              <div>
                <Label>Air Date</Label>
                <Input
                  type="date"
                  value={episodeForm.airDate}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, airDate: e.target.value }))}
                />
              </div>
              <div>
                <Label>Thumbnail URL</Label>
                <Input
                  value={episodeForm.thumbnailUrl}
                  onChange={(e) => setEpisodeForm(prev => ({ ...prev, thumbnailUrl: e.target.value }))}
                  placeholder="https://example.com/thumb.jpg"
                />
              </div>
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setShowAddEpisode(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddEpisode}>
                Add Episode
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Season Dialog */}
      <Dialog open={showAddSeason} onOpenChange={setShowAddSeason}>
        <DialogContent className="max-w-xl">
          <DialogHeader>
            <DialogTitle>Add New Season</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label>Season Number *</Label>
              <Input
                type="number"
                value={seasonForm.seasonNumber}
                onChange={(e) => setSeasonForm(prev => ({ ...prev, seasonNumber: parseInt(e.target.value) || 1 }))}
                min="1"
                placeholder="1"
              />
            </div>

            <div>
              <Label>Season Title (optional)</Label>
              <Input
                value={seasonForm.title}
                onChange={(e) => setSeasonForm(prev => ({ ...prev, title: e.target.value }))}
                placeholder="e.g., The Beginning"
              />
            </div>

            <div>
              <Label>Season Description (optional)</Label>
              <Textarea
                value={seasonForm.description}
                onChange={(e) => setSeasonForm(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Season description"
                rows={3}
              />
            </div>

            <div>
              <Label>Season Poster URL (optional)</Label>
              <Input
                value={seasonForm.posterUrl}
                onChange={(e) => setSeasonForm(prev => ({ ...prev, posterUrl: e.target.value }))}
                placeholder="https://example.com/season-poster.jpg"
              />
            </div>

            <div className="flex justify-end gap-2 pt-4">
              <Button variant="outline" onClick={() => setShowAddSeason(false)}>
                Cancel
              </Button>
              <Button onClick={handleAddSeason}>
                Add Season
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Dialog>
  );
}
