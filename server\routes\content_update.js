// Update content (admin/moderator only)
router.put('/:id', authenticateToken, requireModerator, contentValidation, async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        details: errors.array()
      });
    }

    const { id } = req.params;
    const {
      title,
      type,
      category,
      section,
      tmdbId,
      year,
      genres = [],
      languages = [],
      description,
      posterUrl,
      thumbnailUrl,
      videoLinks,
      secureVideoLinks,
      quality = [],
      tags,
      imdbRating,
      runtime,
      studio,
      audioTracks = [],
      trailer,
      subtitleUrl,
      isPublished = false,
      isFeatured = false,
      addToCarousel = false
    } = req.body;

    // Check if content exists
    const existingResult = await db.execute('SELECT id FROM content WHERE id = ?', [id]);
    let existingContent = [];
    if (Array.isArray(existingResult)) {
      if (Array.isArray(existingResult[0])) {
        existingContent = existingResult[0];
      } else {
        existingContent = existingResult;
      }
    }
    if (!existingContent || existingContent.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    // Convert arrays to JSON strings for database storage using safe function
    const genresJson = safeJsonStringify(genres);
    const languagesJson = safeJsonStringify(languages);
    const qualityJson = safeJsonStringify(quality);
    const audioTracksJson = safeJsonStringify(audioTracks);

    // Convert section slug to section_id
    let sectionId = null;
    if (section) {
      const sectionResult = await db.execute('SELECT id FROM content_sections WHERE slug = ? OR id = ?', [section, section]);
      let sectionRows = [];
      if (Array.isArray(sectionResult)) {
        if (Array.isArray(sectionResult[0])) {
          sectionRows = sectionResult[0];
        } else {
          sectionRows = sectionResult;
        }
      }
      if (sectionRows.length > 0) {
        sectionId = sectionRows[0].id;
      }
    }

    const query = `
      UPDATE content SET
        title = ?, type = ?, category = ?, section_id = ?, tmdb_id = ?, year = ?,
        genres = ?, languages = ?, description = ?, poster_url = ?, thumbnail_url = ?,
        secure_video_links = ?, quality = ?, tags = ?, imdb_rating = ?,
        runtime = ?, studio = ?, audio_tracks = ?, trailer = ?, subtitle_url = ?,
        is_published = ?, is_featured = ?, add_to_carousel = ?, updated_at = NOW()
      WHERE id = ?
    `;

    await db.execute(query, [
      title,
      type,
      category,
      sectionId,
      tmdbId || null,
      year || null,
      genresJson,
      languagesJson,
      description || null,
      posterUrl || null,
      thumbnailUrl || null,
      secureVideoLinks || videoLinks || null,
      qualityJson,
      tags || null,
      imdbRating || null,
      runtime || null,
      studio || null,
      audioTracksJson,
      trailer || null,
      subtitleUrl || null,
      isPublished ? 1 : 0,
      isFeatured ? 1 : 0,
      addToCarousel ? 1 : 0,
      id
    ]);

    res.json({
      success: true,
      message: 'Content updated successfully',
      data: {
        id,
        title,
        type,
        category
      }
    });

  } catch (error) {
    console.error('Error updating content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to update content'
    });
  }
});

// Delete content (admin/moderator only)
router.delete('/:id', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if content exists
    const existingResult = await db.execute('SELECT id FROM content WHERE id = ?', [id]);
    let existingContent = [];
    if (Array.isArray(existingResult)) {
      if (Array.isArray(existingResult[0])) {
        existingContent = existingResult[0];
      } else {
        existingContent = existingResult;
      }
    }
    if (!existingContent || existingContent.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'Not Found',
        message: 'Content not found'
      });
    }

    await db.execute('DELETE FROM content WHERE id = ?', [id]);

    res.json({
      success: true,
      message: 'Content deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting content:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to delete content'
    });
  }
});

// Bulk create content (admin/moderator only)
router.post('/bulk-create', authenticateToken, requireModerator, async (req, res) => {
  try {
    const { items } = req.body;

    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation Error',
        message: 'Items array is required and cannot be empty'
      });
    }

    let created = 0;
    let failed = 0;
    const errors = [];

    for (const item of items) {
      try {
        // Convert section slug to section_id
        let sectionId = null;
        if (item.section) {
          const sectionResult = await db.execute('SELECT id FROM content_sections WHERE slug = ? OR id = ?', [item.section, item.section]);
          let sectionRows = [];
          if (Array.isArray(sectionResult)) {
            if (Array.isArray(sectionResult[0])) {
              sectionRows = sectionResult[0];
            } else {
              sectionRows = sectionResult;
            }
          }
          if (sectionRows.length > 0) {
            sectionId = sectionRows[0].id;
          }
        }

        // Convert arrays to JSON strings for database storage using safe function
        const genresJson = safeJsonStringify(item.genres);
        const languagesJson = safeJsonStringify(item.languages);
        const qualityJson = safeJsonStringify(item.quality);
        const audioTracksJson = safeJsonStringify(item.audioTracks);

        // Generate unique ID for content
        const contentId = `content_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        const query = `
          INSERT INTO content (
            id, title, type, category, section_id, tmdb_id, year, genres, languages,
            description, poster_url, thumbnail_url, secure_video_links, quality, tags,
            imdb_rating, runtime, studio, audio_tracks, trailer, subtitle_url,
            is_published, is_featured, add_to_carousel, created_at, updated_at
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;

        await db.execute(query, [
          contentId,
          item.title,
          item.type,
          item.category,
          sectionId,
          item.tmdbId || null,
          item.year || null,
          genresJson,
          languagesJson,
          item.description || null,
          item.posterUrl || null,
          item.thumbnailUrl || null,
          item.secureVideoLinks || item.videoLinks || null,
          qualityJson,
          item.tags || null,
          item.imdbRating || null,
          item.runtime || null,
          item.studio || null,
          audioTracksJson,
          item.trailer || null,
          item.subtitleUrl || null,
          item.isPublished ? 1 : 0,
          item.isFeatured ? 1 : 0,
          item.addToCarousel ? 1 : 0
        ]);

        created++;
      } catch (error) {
        console.error(`Error creating bulk item ${item.title}:`, error);
        failed++;
        errors.push({
          title: item.title,
          error: error.message
        });
      }
    }

    res.status(201).json({
      success: true,
      message: `Bulk create completed. ${created} items created, ${failed} items failed.`,
      data: {
        created,
        failed,
        total: items.length,
        errors
      }
    });

  } catch (error) {
    console.error('Error in bulk create:', error);
    res.status(500).json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to bulk create content'
    });
  }
});
