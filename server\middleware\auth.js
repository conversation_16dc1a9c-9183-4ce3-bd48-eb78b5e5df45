const jwt = require('jsonwebtoken');
const db = require('../config/database');

// Middleware to authenticate JWT token
const authenticateToken = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Access token is required'
      });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    // Check if user still exists and is active
    const userQuery = 'SELECT id, username, role, is_active FROM admin_users WHERE id = ?';
    const userResult = await db.execute(userQuery, [decoded.userId]);

    // Handle database result properly
    let users = [];
    if (Array.isArray(userResult)) {
      users = userResult;
    }

    if (users.length === 0 || !users[0].is_active) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User not found or inactive'
      });
    }

    const user = users[0];

    // Add user info to request object
    req.user = {
      userId: user.id,
      username: user.username,
      role: user.role
    };

    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid token'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Token has expired'
      });
    }

    console.error('Authentication error:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed'
    });
  }
};

// Middleware to require admin role
const requireAdmin = (req, res, next) => {
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Admin access required'
    });
  }
  next();
};

// Middleware to require admin or moderator role
const requireModerator = (req, res, next) => {
  if (!['admin', 'moderator'].includes(req.user.role)) {
    return res.status(403).json({
      error: 'Forbidden',
      message: 'Moderator or admin access required'
    });
  }
  next();
};

// Middleware to check specific permissions
const requirePermission = (permission) => {
  return async (req, res, next) => {
    try {
      const userQuery = 'SELECT permissions FROM admin_users WHERE id = ?';
      const userResult = await db.execute(userQuery, [req.user.userId]);

      // Handle database result properly
      let users = [];
      if (Array.isArray(userResult)) {
        users = userResult;
      }

      if (users.length === 0) {
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'User not found'
        });
      }

      const user = users[0];

      const permissions = user.permissions ? JSON.parse(user.permissions) : [];
      
      // Admin has all permissions
      if (req.user.role === 'admin' || permissions.includes(permission)) {
        next();
      } else {
        return res.status(403).json({
          error: 'Forbidden',
          message: `Permission '${permission}' required`
        });
      }
    } catch (error) {
      console.error('Permission check error:', error);
      return res.status(500).json({
        error: 'Internal Server Error',
        message: 'Permission check failed'
      });
    }
  };
};

// Optional authentication (for public endpoints that can benefit from user context)
const optionalAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      req.user = null;
      return next();
    }

    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
    
    const userQuery = 'SELECT id, username, role, is_active FROM admin_users WHERE id = ?';
    const userResult = await db.execute(userQuery, [decoded.userId]);

    // Handle database result properly
    let users = [];
    if (Array.isArray(userResult)) {
      users = userResult;
    }

    if (users.length > 0 && users[0].is_active) {
      const user = users[0];
      req.user = {
        userId: user.id,
        username: user.username,
        role: user.role
      };
    } else {
      req.user = null;
    }

    next();
  } catch (error) {
    // If token is invalid, just continue without user context
    req.user = null;
    next();
  }
};

// Rate limiting for sensitive operations
const sensitiveOperationLimit = (req, res, next) => {
  // This could be enhanced with Redis for distributed rate limiting
  // For now, we'll use a simple in-memory approach
  
  const key = `${req.ip}_${req.user?.userId || 'anonymous'}`;
  const now = Date.now();
  const windowMs = 60 * 1000; // 1 minute
  const maxAttempts = 10;

  if (!global.sensitiveOpLimits) {
    global.sensitiveOpLimits = new Map();
  }

  const userAttempts = global.sensitiveOpLimits.get(key) || [];
  const recentAttempts = userAttempts.filter(timestamp => now - timestamp < windowMs);

  if (recentAttempts.length >= maxAttempts) {
    return res.status(429).json({
      error: 'Too Many Requests',
      message: 'Too many sensitive operations. Please try again later.'
    });
  }

  recentAttempts.push(now);
  global.sensitiveOpLimits.set(key, recentAttempts);

  // Clean up old entries periodically
  if (Math.random() < 0.01) { // 1% chance
    for (const [k, attempts] of global.sensitiveOpLimits.entries()) {
      const validAttempts = attempts.filter(timestamp => now - timestamp < windowMs);
      if (validAttempts.length === 0) {
        global.sensitiveOpLimits.delete(k);
      } else {
        global.sensitiveOpLimits.set(k, validAttempts);
      }
    }
  }

  next();
};

// Middleware to log admin actions
const logAdminAction = (action) => {
  return async (req, res, next) => {
    try {
      const clientIP = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent') || '';
      
      // Log the action
      const query = `
        INSERT INTO admin_security_logs (user_id, action, ip_address, user_agent, details, created_at)
        VALUES (?, ?, ?, ?, ?, NOW())
      `;
      
      const details = {
        endpoint: req.originalUrl,
        method: req.method,
        body: req.method !== 'GET' ? req.body : undefined,
        query: req.query
      };

      await db.execute(query, [
        req.user.userId,
        action,
        clientIP,
        userAgent,
        JSON.stringify(details)
      ]);

      next();
    } catch (error) {
      console.error('Error logging admin action:', error);
      // Don't fail the request if logging fails
      next();
    }
  };
};

// Session-based authentication (alternative to JWT)
const authenticateSession = async (req, res, next) => {
  try {
    if (!req.session || !req.session.userId) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid session required'
      });
    }

    // Check if user still exists and is active
    const userQuery = 'SELECT id, username, role, is_active FROM admin_users WHERE id = ?';
    const userResult = await db.execute(userQuery, [req.session.userId]);

    // Handle database result properly
    let users = [];
    if (Array.isArray(userResult)) {
      users = userResult;
    }

    if (users.length === 0 || !users[0].is_active) {
      req.session.destroy();
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'User not found or inactive'
      });
    }

    const user = users[0];

    req.user = {
      userId: user.id,
      username: user.username,
      role: user.role
    };

    next();
  } catch (error) {
    console.error('Session authentication error:', error);
    return res.status(500).json({
      error: 'Internal Server Error',
      message: 'Authentication failed'
    });
  }
};

module.exports = {
  authenticateToken,
  authenticateSession,
  requireAdmin,
  requireModerator,
  requirePermission,
  optionalAuth,
  sensitiveOperationLimit,
  logAdminAction
};
