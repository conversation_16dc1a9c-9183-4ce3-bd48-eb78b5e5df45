import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Play, Star, Calendar, Clock, Eye, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import CardGrid from '@/components/CardGrid';
import { MediaItem } from '@/types/media';
import { scrollToTop } from '@/utils/scrollToTop';
import apiService from '@/services/apiService';

export default function ContentPreview() {
  const [contentData, setContentData] = useState<MediaItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchContent = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await apiService.getContent({
          published: true,
          limit: 100 // Get enough content for preview
        });

        if (response && response.success && Array.isArray(response.data)) {
          setContentData(response.data);
        } else {
          setContentData([]);
        }
      } catch (error) {
        console.error('Error fetching content:', error);
        setError('Failed to load content');
        setContentData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, []);

  // Get enhanced content items that have video links for demonstration
  const moviesWithVideo = contentData.filter(item =>
    item.type === 'movie' && item.secureVideoLinks
  ).slice(0, 6);

  const seriesWithVideo = contentData.filter(item =>
    item.type === 'series' && item.secureVideoLinks
  ).slice(0, 6);

  const featuredContent = contentData.filter(item =>
    item.isFeatured && item.secureVideoLinks
  ).slice(0, 4);

  if (loading) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <div className="h-7 md:h-10" />
        <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center text-muted-foreground">
              <div className="text-4xl mb-4">🎬</div>
              <p className="text-lg">Loading preview content...</p>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col min-h-screen bg-background">
        <Header />
        <div className="h-7 md:h-10" />
        <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center text-muted-foreground">
              <div className="text-4xl mb-4">⚠️</div>
              <p className="text-lg mb-2">Failed to load preview content</p>
              <p className="text-sm">{error}</p>
              <Button
                onClick={() => window.location.reload()}
                className="mt-4"
              >
                Retry
              </Button>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      
      {/* Added spacing after Header */}
      <div className="h-7 md:h-10" />

      <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
        {/* Preview Header */}
        <div className="mb-8 sm:mb-12">
          <div className="flex items-center gap-4 mb-6">
            <Link
              to="/admin"
              onClick={scrollToTop}
              className="inline-flex items-center gap-2 px-4 py-2 border border-border rounded-md hover:bg-muted transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Admin
            </Link>
            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30">
              <Eye className="w-3 h-3 mr-1" />
              Preview Mode
            </Badge>
          </div>

          <div className="text-center mb-8">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-black font-mono text-primary drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)] tracking-wide uppercase leading-tight mb-4">
              Content Preview System
            </h1>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              This preview demonstrates how content added via the admin panel appears to end users. 
              All content shown includes integrated video players with secure embed link protection.
            </p>
          </div>

          {/* Preview Features */}
          <Card className="bg-card/95 backdrop-blur-sm border-border/50 shadow-xl">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-primary">
                <Settings className="w-5 h-5" />
                Preview Features Demonstrated
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">Enhanced Metadata</h4>
                    <p className="text-sm text-muted-foreground">TMDB IDs, ratings, runtime, studio info</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">Secure Video Players</h4>
                    <p className="text-sm text-muted-foreground">Multiple embed sources with protection</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">Season/Episode Structure</h4>
                    <p className="text-sm text-muted-foreground">Complete web series management</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">Mobile Responsive</h4>
                    <p className="text-sm text-muted-foreground">Optimized for all screen sizes</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">Dark Theme Integration</h4>
                    <p className="text-sm text-muted-foreground">Consistent with existing design</p>
                  </div>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-foreground">Content Navigation</h4>
                    <p className="text-sm text-muted-foreground">Direct links to individual pages</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Featured Content Section */}
        {featuredContent.length > 0 && (
          <section className="stdb-section mb-10 sm:mb-14">
            <div className="flex items-end justify-between mb-3 sm:mb-4">
              <h3 className="text-2xl sm:text-3xl md:text-4xl font-black font-mono text-primary drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)] tracking-wide uppercase leading-tight select-none">
                Featured Content with Video Players
              </h3>
              <Badge variant="outline" className="text-primary border-primary/30">
                {featuredContent.length} items
              </Badge>
            </div>
            <CardGrid items={featuredContent} />
            <div className="mt-6 text-center">
              <p className="text-sm text-muted-foreground">
                Click any card to view the full content page with integrated video player
              </p>
            </div>
          </section>
        )}

        {/* Movies Section */}
        {moviesWithVideo.length > 0 && (
          <section className="stdb-section mb-10 sm:mb-14">
            <div className="flex items-end justify-between mb-3 sm:mb-4">
              <h3 className="text-2xl sm:text-3xl md:text-4xl font-black font-mono text-primary drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)] tracking-wide uppercase leading-tight select-none">
                Movies with Enhanced Metadata
              </h3>
              <Badge variant="outline" className="text-primary border-primary/30">
                {moviesWithVideo.length} items
              </Badge>
            </div>
            <CardGrid items={moviesWithVideo} />
            <div className="mt-6 p-4 bg-muted/30 rounded-lg border border-border/50">
              <h4 className="font-medium text-foreground mb-2">Enhanced Movie Features:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Multiple video embed sources with automatic player selection</li>
                <li>• Comprehensive metadata including IMDB ratings, runtime, and studio information</li>
                <li>• Secure video link encoding to prevent source code inspection</li>
                <li>• Responsive video player with mobile-optimized aspect ratios</li>
              </ul>
            </div>
          </section>
        )}

        {/* Web Series Section */}
        {seriesWithVideo.length > 0 && (
          <section className="stdb-section mb-10 sm:mb-14">
            <div className="flex items-end justify-between mb-3 sm:mb-4">
              <h3 className="text-2xl sm:text-3xl md:text-4xl font-black font-mono text-primary drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)] tracking-wide uppercase leading-tight select-none">
                Web Series with Season/Episode Management
              </h3>
              <Badge variant="outline" className="text-primary border-primary/30">
                {seriesWithVideo.length} items
              </Badge>
            </div>
            <CardGrid items={seriesWithVideo} />
            <div className="mt-6 p-4 bg-muted/30 rounded-lg border border-border/50">
              <h4 className="font-medium text-foreground mb-2">Enhanced Series Features:</h4>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Complete season and episode structure with individual video links</li>
                <li>• Episode-specific metadata including air dates and descriptions</li>
                <li>• Series overview video player plus individual episode players</li>
                <li>• Organized episode navigation with play buttons for each episode</li>
              </ul>
            </div>
          </section>
        )}

        {/* Admin Workflow Demo */}
        <section className="stdb-section mb-10 sm:mb-14">
          <Card className="bg-card/95 backdrop-blur-sm border-border/50 shadow-xl">
            <CardHeader>
              <CardTitle className="text-primary">Admin Panel → User Experience Workflow</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-primary font-bold">1</span>
                  </div>
                  <h4 className="font-medium text-foreground mb-2">Admin Adds Content</h4>
                  <p className="text-sm text-muted-foreground">
                    Content is added via the admin panel with video links, metadata, and episode structure
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-primary font-bold">2</span>
                  </div>
                  <h4 className="font-medium text-foreground mb-2">Automatic Processing</h4>
                  <p className="text-sm text-muted-foreground">
                    Video links are automatically encoded for security and validated for compatibility
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-primary font-bold">3</span>
                  </div>
                  <h4 className="font-medium text-foreground mb-2">User Experience</h4>
                  <p className="text-sm text-muted-foreground">
                    Content appears on the website with fully functional video players and rich metadata
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Quick Actions */}
        <section className="text-center mb-8">
          <h3 className="text-xl font-bold text-foreground mb-4">Quick Actions</h3>
          <div className="flex flex-wrap justify-center gap-4">
            <Link to="/admin" onClick={scrollToTop}>
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
                <Settings className="w-4 h-4 mr-2" />
                Return to Admin Panel
              </Button>
            </Link>
            <Link to="/content/1" onClick={scrollToTop}>
              <Button variant="outline" className="border-primary/30 text-primary hover:bg-primary/10">
                <Play className="w-4 h-4 mr-2" />
                View Sample Movie
              </Button>
            </Link>
            <Link to="/content/2" onClick={scrollToTop}>
              <Button variant="outline" className="border-primary/30 text-primary hover:bg-primary/10">
                <Play className="w-4 h-4 mr-2" />
                View Sample Series
              </Button>
            </Link>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
